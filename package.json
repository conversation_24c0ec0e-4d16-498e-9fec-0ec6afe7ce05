{"name": "routine-mail-sveltekit", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "npx drizzle-kit generate && npx drizzle-kit migrate && vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:generate": "drizzle-kit generate", "db:migrate": "tsx scripts/migrate.ts", "db:studio": "drizzle-kit studio", "migrate": "npx drizzle-kit push"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "drizzle-kit": "^0.31.4", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tsx": "^4.20.3", "typescript": "^5.0.0", "vite": "^7.0.4"}, "dependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "dotenv": "^17.2.0", "drizzle-orm": "^0.44.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.5", "pg": "^8.16.3", "postgres": "^3.4.7", "uuid": "^11.1.0"}}